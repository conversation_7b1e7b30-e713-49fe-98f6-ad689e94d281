import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { ExtraMessageImpl, PopupButtonImpl, PopupButtonGameActions } from "@skywind-group/sw-wallet-adapter-core";

@suite
class ExtraMessageImplSpec {

    @test
    public testHideButtonsReturnsFalseWhenHidden() {
        // Arrange
        const extraMessage = ExtraMessageImpl.create();
        const button = PopupButtonImpl.create()
            .setLabel("Test Button")
            .setGameAction(PopupButtonGameActions.continue);

        extraMessage.addButton(button);

        // Act
        extraMessage.hidePopupButtons();

        // Assert
        expect(extraMessage.buttons).to.be.false;
    }

    @test
    public testButtonsReturnsArrayWhenNotHidden() {
        // Arrange
        const extraMessage = ExtraMessageImpl.create();
        const button = PopupButtonImpl.create()
            .setLabel("Test Button")
            .setGameAction(PopupButtonGameActions.continue);

        extraMessage.addButton(button);

        // Assert
        expect(extraMessage.buttons).to.be.an('array');
        expect(extraMessage.buttons).to.have.length(1);
        expect((extraMessage.buttons as any[])[0].label).to.equal("Test Button");
    }

    @test
    public testShowPopupButtonsRestoresButtons() {
        // Arrange
        const extraMessage = ExtraMessageImpl.create();
        const button = PopupButtonImpl.create()
            .setLabel("Test Button")
            .setGameAction(PopupButtonGameActions.continue);

        extraMessage.addButton(button);
        extraMessage.hidePopupButtons();

        // Act
        extraMessage.showPopupButtons();

        // Assert
        expect(extraMessage.buttons).to.be.an('array');
        expect(extraMessage.buttons).to.have.length(1);
        expect((extraMessage.buttons as any[])[0].label).to.equal("Test Button");
    }

    @test
    public testHideButtonsReturnsChainableInstance() {
        // Arrange
        const extraMessage = ExtraMessageImpl.create();

        // Act
        const result = extraMessage.hidePopupButtons();

        // Assert
        expect(result).to.equal(extraMessage);
    }

    @test
    public testShowPopupButtonsReturnsChainableInstance() {
        // Arrange
        const extraMessage = ExtraMessageImpl.create();

        // Act
        const result = extraMessage.showPopupButtons();

        // Assert
        expect(result).to.equal(extraMessage);
    }

    @test
    public testButtonsReturnsFalseWhenHiddenEvenWithMultipleButtons() {
        // Arrange
        const extraMessage = ExtraMessageImpl.create();
        const button1 = PopupButtonImpl.create()
            .setLabel("Button 1")
            .setGameAction(PopupButtonGameActions.continue);
        const button2 = PopupButtonImpl.create()
            .setLabel("Button 2")
            .setGameAction(PopupButtonGameActions.lobby);

        extraMessage.addButton(button1);
        extraMessage.addButton(button2);

        // Act
        extraMessage.hidePopupButtons();

        // Assert
        expect(extraMessage.buttons).to.be.false;
    }

    @test
    public testButtonsReturnsEmptyArrayWhenNoButtonsAdded() {
        // Arrange
        const extraMessage = ExtraMessageImpl.create();

        // Assert
        expect(extraMessage.buttons).to.be.an('array');
        expect(extraMessage.buttons).to.have.length(0);
    }
}
